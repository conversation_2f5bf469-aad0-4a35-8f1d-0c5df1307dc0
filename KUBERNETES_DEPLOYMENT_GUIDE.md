# Kubernetes Deployment Guide for Workforce Management System

This guide provides comprehensive instructions for deploying the Workforce Management microservices architecture to Kubernetes.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Architecture Overview](#architecture-overview)
3. [Quick Start](#quick-start)
4. [Detailed Deployment Steps](#detailed-deployment-steps)
5. [Configuration](#configuration)
6. [Accessing the Application](#accessing-the-application)
7. [Monitoring and Troubleshooting](#monitoring-and-troubleshooting)
8. [Scaling](#scaling)
9. [Maintenance](#maintenance)
10. [Cleanup](#cleanup)

## 🔧 Prerequisites

### Required Software
- **Kubernetes cluster** (v1.20+)
- **kubectl** configured to access your cluster
- **Docker** (for building images)
- **Maven** (for building Spring Boot applications)
- **Node.js & npm** (for building React frontend)

### Optional Components
- **NGINX Ingress Controller** (for domain-based routing)
- **Metrics Server** (for Horizontal Pod Autoscaling)
- **Helm** (alternative deployment method)

### Cluster Requirements
- **Minimum Resources**: 8 CPU cores, 16GB RAM
- **Storage**: 20GB for PostgreSQL data
- **Network**: Support for LoadBalancer services (or NodePort as alternative)

## 🏗️ Architecture Overview

### Microservices
- **api-gateway** (Port 8080) - API Gateway and routing
- **customer-service** (Port 8081) - Customer management
- **job-service** (Port 8082) - Job and job category management
- **customer-contract-service** (Port 8083) - Contract management
- **customer-payment-service** (Port 8084) - Payment processing
- **customer-statistics-service** (Port 8085) - Statistics and reporting
- **frontend** (Port 3000) - React web application

### Infrastructure
- **PostgreSQL** - Database for all microservices
- **ConfigMaps** - Environment configuration
- **Secrets** - Sensitive data (passwords, keys)
- **Persistent Volumes** - Database storage
- **Services** - Internal and external networking
- **Ingress** - External access routing

## 🚀 Quick Start

### 1. Clone and Prepare
```bash
cd /path/to/Microservice_With_Kubernetes
```

### 2. Build All Images
```bash
chmod +x k8s/build-images.sh
./k8s/build-images.sh
```

### 3. Deploy to Kubernetes
```bash
chmod +x k8s/deploy.sh
./k8s/deploy.sh
```

### 4. Access the Application
```bash
# Get external IPs
kubectl get services -n microservice-system

# Or use port forwarding
kubectl port-forward service/frontend-service 3000:3000 -n microservice-system
kubectl port-forward service/api-gateway-service 8080:8080 -n microservice-system
```

## 📝 Detailed Deployment Steps

### Step 1: Build Docker Images

First, ensure all applications are built:

```bash
# Build Spring Boot applications
for service in api-gateway customer-service job-service customer-contract-service customer-payment-service customer-statistics-service; do
    cd $service
    mvn clean package -DskipTests
    cd ..
done

# Build React frontend
cd microservice_fe
npm install
npm run build
cd ..

# Build all Docker images
./k8s/build-images.sh
```

### Step 2: Prepare Kubernetes Resources

```bash
cd k8s

# Create namespace
kubectl apply -f namespace.yaml

# Create secrets (database passwords, etc.)
kubectl apply -f secrets.yaml

# Create configuration maps
kubectl apply -f configmaps.yaml
```

### Step 3: Deploy Storage

```bash
# Create storage resources for PostgreSQL
kubectl apply -f postgres-storage.yaml

# Deploy PostgreSQL database
kubectl apply -f postgres-deployment.yaml

# Wait for PostgreSQL to be ready
kubectl wait --for=condition=ready pod -l app=postgres -n microservice-system --timeout=300s
```

### Step 4: Deploy Microservices

Deploy in dependency order:

```bash
# Deploy base services
kubectl apply -f customer-service-deployment.yaml
kubectl apply -f job-service-deployment.yaml

# Wait for base services
kubectl wait --for=condition=ready pod -l app=customer-service -n microservice-system --timeout=300s
kubectl wait --for=condition=ready pod -l app=job-service -n microservice-system --timeout=300s

# Deploy dependent services
kubectl apply -f customer-contract-service-deployment.yaml
kubectl apply -f customer-payment-service-deployment.yaml
kubectl apply -f customer-statistics-service-deployment.yaml

# Deploy API Gateway
kubectl apply -f api-gateway-deployment.yaml

# Deploy Frontend
kubectl apply -f frontend-deployment.yaml
```

### Step 5: Configure External Access

```bash
# Deploy Ingress (if NGINX Ingress Controller is available)
kubectl apply -f ingress.yaml

# Deploy Horizontal Pod Autoscalers (if Metrics Server is available)
kubectl apply -f hpa.yaml

# Deploy Network Policies (for security)
kubectl apply -f network-policy.yaml
```

## ⚙️ Configuration

### Environment Variables

Each microservice is configured via ConfigMaps. Key configurations:

- **Database URLs**: Point to internal PostgreSQL service
- **Service URLs**: Use Kubernetes service names for inter-service communication
- **Profiles**: Set to "kubernetes" for Kubernetes-specific configurations

### Secrets Management

Sensitive data is stored in Kubernetes Secrets:
- Database passwords
- JWT secrets
- API keys

### Resource Limits

Each service has defined resource requests and limits:
- **Requests**: Guaranteed resources
- **Limits**: Maximum resources allowed

## 🌐 Accessing the Application

### LoadBalancer Services

If your cluster supports LoadBalancer services:

```bash
# Get external IPs
kubectl get services -n microservice-system

# Access via external IPs
# Frontend: http://<FRONTEND_EXTERNAL_IP>:3000
# API Gateway: http://<API_GATEWAY_EXTERNAL_IP>:8080
```

### Port Forwarding

For local development or clusters without LoadBalancer support:

```bash
# Frontend access
kubectl port-forward service/frontend-service 3000:3000 -n microservice-system

# API Gateway access
kubectl port-forward service/api-gateway-service 8080:8080 -n microservice-system

# Direct service access (for debugging)
kubectl port-forward service/customer-service 8081:8081 -n microservice-system
```

### Ingress Access

If Ingress is configured:

```bash
# Add to /etc/hosts (or equivalent)
echo "<INGRESS_IP> workforce-management.local" >> /etc/hosts
echo "<INGRESS_IP> api.workforce-management.local" >> /etc/hosts

# Access via domain names
# Frontend: http://workforce-management.local
# API: http://api.workforce-management.local
```

## 📊 Monitoring and Troubleshooting

### Check Deployment Status

```bash
# Overview of all resources
kubectl get all -n microservice-system

# Check pod status
kubectl get pods -n microservice-system

# Check service endpoints
kubectl get endpoints -n microservice-system
```

### View Logs

```bash
# View logs for specific service
kubectl logs -f deployment/customer-service -n microservice-system

# View logs for all containers in a pod
kubectl logs -f <pod-name> --all-containers -n microservice-system

# View previous container logs
kubectl logs <pod-name> --previous -n microservice-system
```

### Debug Connectivity

```bash
# Test service connectivity
kubectl exec -it <pod-name> -n microservice-system -- curl http://customer-service:8081/actuator/health

# Check DNS resolution
kubectl exec -it <pod-name> -n microservice-system -- nslookup customer-service
```

### Common Issues

1. **Pods stuck in Pending**: Check resource availability and storage
2. **Services not accessible**: Verify service selectors and port configurations
3. **Database connection issues**: Check PostgreSQL pod status and network policies
4. **Image pull errors**: Ensure images are built and available

## 📈 Scaling

### Manual Scaling

```bash
# Scale specific service
kubectl scale deployment customer-service --replicas=3 -n microservice-system

# Scale multiple services
kubectl scale deployment customer-service job-service --replicas=3 -n microservice-system
```

### Horizontal Pod Autoscaling

HPA is configured for automatic scaling based on CPU and memory usage:

```bash
# Check HPA status
kubectl get hpa -n microservice-system

# View HPA details
kubectl describe hpa customer-service-hpa -n microservice-system
```

## 🔧 Maintenance

### Update Applications

```bash
# Build new image
docker build -t customer-service:v2.0 ./customer-service

# Update deployment
kubectl set image deployment/customer-service customer-service=customer-service:v2.0 -n microservice-system

# Check rollout status
kubectl rollout status deployment/customer-service -n microservice-system
```

### Database Maintenance

```bash
# Access PostgreSQL
kubectl exec -it postgres-0 -n microservice-system -- psql -U postgres

# Backup database
kubectl exec postgres-0 -n microservice-system -- pg_dump -U postgres customerdb > backup.sql

# Restore database
kubectl exec -i postgres-0 -n microservice-system -- psql -U postgres customerdb < backup.sql
```

## 🧹 Cleanup

### Remove All Resources

```bash
# Use the undeploy script
chmod +x k8s/undeploy.sh
./k8s/undeploy.sh
```

### Manual Cleanup

```bash
# Delete all resources in namespace
kubectl delete all --all -n microservice-system

# Delete persistent volumes
kubectl delete pvc --all -n microservice-system

# Delete namespace
kubectl delete namespace microservice-system
```

## 📚 Additional Resources

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Spring Boot on Kubernetes](https://spring.io/guides/gs/spring-boot-kubernetes/)
- [React Deployment Guide](https://create-react-app.dev/docs/deployment/)
- [PostgreSQL on Kubernetes](https://kubernetes.io/docs/tutorials/stateful-application/postgresql/)

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review Kubernetes and application logs
3. Verify cluster resources and connectivity
4. Consult the project documentation
