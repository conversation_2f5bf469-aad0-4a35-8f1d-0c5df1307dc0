# Kubernetes Deployment for Workforce Management System

This directory contains all the necessary Kubernetes manifests and scripts to deploy the Workforce Management microservices system to a Kubernetes cluster.

## 📁 Directory Structure

```
k8s/
├── README.md                                    # This file
├── namespace.yaml                               # Kubernetes namespace
├── secrets.yaml                                 # Database passwords and secrets
├── configmaps.yaml                             # Environment configuration
├── postgres-storage.yaml                       # PostgreSQL storage configuration
├── postgres-deployment.yaml                    # PostgreSQL database deployment
├── api-gateway-deployment.yaml                 # API Gateway service
├── customer-service-deployment.yaml            # Customer management service
├── job-service-deployment.yaml                 # Job management service
├── customer-contract-service-deployment.yaml   # Contract management service
├── customer-payment-service-deployment.yaml    # Payment processing service
├── customer-statistics-service-deployment.yaml # Statistics service
├── frontend-deployment.yaml                    # React frontend application
├── ingress.yaml                                # External access routing
├── hpa.yaml                                    # Horizontal Pod Autoscaling
├── network-policy.yaml                        # Network security policies
├── monitoring.yaml                             # Monitoring configuration
├── debug-tools.yaml                           # Debug and troubleshooting tools
├── setup.sh                                   # Environment setup script
├── build-images.sh                            # Docker image build script
├── deploy.sh                                  # Main deployment script
├── undeploy.sh                                # Cleanup script
├── status.sh                                  # Status checking script
└── logs.sh                                    # Log viewing script
```

## 🚀 Quick Start

### 1. Setup Environment
```bash
chmod +x k8s/setup.sh
./k8s/setup.sh
```

### 2. Build Docker Images
```bash
./k8s/build-images.sh
```

### 3. Deploy to Kubernetes
```bash
./k8s/deploy.sh
```

### 4. Check Status
```bash
./k8s/status.sh
```

## 📋 Prerequisites

- **Kubernetes cluster** (v1.20+) with kubectl configured
- **Docker** for building images
- **Maven** for building Spring Boot applications
- **Node.js & npm** for building React frontend
- **8+ CPU cores and 16+ GB RAM** in your cluster
- **20+ GB storage** for PostgreSQL data

### Optional Components
- **NGINX Ingress Controller** for domain-based routing
- **Metrics Server** for Horizontal Pod Autoscaling
- **Prometheus** for monitoring (if using monitoring.yaml)

## 🏗️ Architecture

### Services Deployed
- **PostgreSQL** - Centralized database
- **API Gateway** (Port 8080) - Entry point and routing
- **Customer Service** (Port 8081) - Customer management
- **Job Service** (Port 8082) - Job and category management
- **Customer Contract Service** (Port 8083) - Contract management
- **Customer Payment Service** (Port 8084) - Payment processing
- **Customer Statistics Service** (Port 8085) - Statistics and reporting
- **Frontend** (Port 3000) - React web application

### Networking
- **ClusterIP** services for internal communication
- **LoadBalancer** services for external access (API Gateway and Frontend)
- **Ingress** for domain-based routing (optional)
- **Network Policies** for security

## 🔧 Configuration

### Environment Variables
All services are configured via ConfigMaps with environment-specific settings:
- Database connection strings
- Service URLs for inter-service communication
- Application-specific configurations

### Secrets
Sensitive data is stored in Kubernetes Secrets:
- Database passwords
- JWT secrets
- API keys

### Resource Management
Each service has defined:
- **Resource requests** (guaranteed resources)
- **Resource limits** (maximum allowed)
- **Health checks** (liveness and readiness probes)
- **Autoscaling** policies

## 📊 Monitoring and Debugging

### Status Checking
```bash
./k8s/status.sh
```

### Log Viewing
```bash
# View logs from all services
./k8s/logs.sh -a

# Follow logs from specific service
./k8s/logs.sh -f customer-service

# View last 50 lines from API Gateway
./k8s/logs.sh -l 50 api-gateway
```

### Debug Tools
Deploy debug tools for troubleshooting:
```bash
kubectl apply -f debug-tools.yaml

# Network debugging
kubectl exec -it debug-tools -n microservice-system -- curl http://customer-service:8081/actuator/health

# Database access
kubectl exec -it postgres-client -n microservice-system -- psql -h postgres-service -U postgres
```

## 🌐 Accessing the Application

### LoadBalancer Access
```bash
# Get external IPs
kubectl get services -n microservice-system

# Access via external IPs
# Frontend: http://<FRONTEND_EXTERNAL_IP>:3000
# API Gateway: http://<API_GATEWAY_EXTERNAL_IP>:8080
```

### Port Forwarding
```bash
# Frontend
kubectl port-forward service/frontend-service 3000:3000 -n microservice-system

# API Gateway
kubectl port-forward service/api-gateway-service 8080:8080 -n microservice-system
```

### Ingress Access (if configured)
```bash
# Add to /etc/hosts
echo "<INGRESS_IP> workforce-management.local" >> /etc/hosts

# Access via domain
# Frontend: http://workforce-management.local
# API: http://api.workforce-management.local
```

## 📈 Scaling

### Manual Scaling
```bash
# Scale specific service
kubectl scale deployment customer-service --replicas=3 -n microservice-system

# Scale multiple services
kubectl scale deployment customer-service job-service --replicas=3 -n microservice-system
```

### Automatic Scaling
Horizontal Pod Autoscaling (HPA) is configured for all services:
- **CPU threshold**: 70%
- **Memory threshold**: 80%
- **Min replicas**: 2
- **Max replicas**: 6-10 (varies by service)

## 🔒 Security

### Network Policies
Network policies are configured to:
- Restrict database access to authorized services only
- Allow API Gateway to communicate with all microservices
- Allow frontend to communicate with API Gateway only
- Block unauthorized inter-service communication

### Security Context
All pods run with:
- Non-root user (UID 1000)
- Read-only root filesystem where possible
- Dropped capabilities

## 🧹 Cleanup

### Remove All Resources
```bash
./k8s/undeploy.sh
```

### Manual Cleanup
```bash
# Delete all resources in namespace
kubectl delete all --all -n microservice-system

# Delete persistent volumes
kubectl delete pvc --all -n microservice-system

# Delete namespace
kubectl delete namespace microservice-system
```

## 🔧 Troubleshooting

### Common Issues

1. **Pods stuck in Pending**
   - Check cluster resources: `kubectl describe nodes`
   - Check storage availability: `kubectl get pv,pvc`

2. **Services not accessible**
   - Verify service selectors: `kubectl get endpoints -n microservice-system`
   - Check network policies: `kubectl get networkpolicy -n microservice-system`

3. **Database connection issues**
   - Check PostgreSQL status: `kubectl logs postgres-0 -n microservice-system`
   - Verify database credentials in secrets

4. **Image pull errors**
   - Ensure images are built: `docker images | grep -E "(api-gateway|customer-service|job-service|customer-contract-service|customer-payment-service|customer-statistics-service|frontend)"`
   - Check image pull policy in deployments

### Debug Commands
```bash
# Check pod details
kubectl describe pod <pod-name> -n microservice-system

# Check service endpoints
kubectl get endpoints -n microservice-system

# Test connectivity
kubectl exec -it debug-tools -n microservice-system -- nc -zv customer-service 8081

# View events
kubectl get events -n microservice-system --sort-by='.lastTimestamp'
```

## 📚 Additional Resources

- [Main Deployment Guide](../KUBERNETES_DEPLOYMENT_GUIDE.md)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Spring Boot on Kubernetes](https://spring.io/guides/gs/spring-boot-kubernetes/)
- [React Deployment](https://create-react-app.dev/docs/deployment/)

## 🆘 Support

For issues and questions:
1. Run `./k8s/status.sh` to check system status
2. Use `./k8s/logs.sh -a` to view all service logs
3. Check the troubleshooting section above
4. Review Kubernetes events and pod descriptions
