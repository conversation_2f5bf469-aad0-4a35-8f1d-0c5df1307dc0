#!/bin/bash

# Kubernetes Status Check Script for Workforce Management System
# This script provides comprehensive status information about the deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

print_section() {
    echo -e "\n${BLUE}--- $1 ---${NC}"
}

print_status() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
    exit 1
fi

print_header "WORKFORCE MANAGEMENT SYSTEM - KUBERNETES STATUS"

# Check if namespace exists
if ! kubectl get namespace microservice-system &> /dev/null; then
    print_error "Namespace 'microservice-system' does not exist"
    print_status "Run './deploy.sh' to deploy the system"
    exit 1
fi

print_success "Namespace 'microservice-system' exists"

# Cluster Information
print_section "Cluster Information"
kubectl cluster-info
echo ""
kubectl get nodes -o wide

# Namespace Resources Overview
print_section "Namespace Resources Overview"
kubectl get all -n microservice-system

# Pod Status Details
print_section "Pod Status Details"
kubectl get pods -n microservice-system -o wide

# Service Status
print_section "Service Status"
kubectl get services -n microservice-system -o wide

# Ingress Status
print_section "Ingress Status"
if kubectl get ingress -n microservice-system &> /dev/null; then
    kubectl get ingress -n microservice-system
else
    print_warning "No Ingress resources found"
fi

# ConfigMaps and Secrets
print_section "ConfigMaps and Secrets"
echo "ConfigMaps:"
kubectl get configmaps -n microservice-system
echo ""
echo "Secrets:"
kubectl get secrets -n microservice-system

# Persistent Volumes
print_section "Storage"
echo "Persistent Volumes:"
kubectl get pv
echo ""
echo "Persistent Volume Claims:"
kubectl get pvc -n microservice-system

# HPA Status
print_section "Horizontal Pod Autoscaler Status"
if kubectl get hpa -n microservice-system &> /dev/null; then
    kubectl get hpa -n microservice-system
    echo ""
    kubectl top pods -n microservice-system 2>/dev/null || print_warning "Metrics server not available"
else
    print_warning "No HPA resources found"
fi

# Network Policies
print_section "Network Policies"
if kubectl get networkpolicy -n microservice-system &> /dev/null; then
    kubectl get networkpolicy -n microservice-system
else
    print_warning "No Network Policies found"
fi

# Health Checks
print_section "Service Health Checks"

services=("postgres-service:5432" "customer-service:8081" "job-service:8082" "customer-contract-service:8083" "customer-payment-service:8084" "customer-statistics-service:8085" "api-gateway-service:8080" "frontend-service:3000")

for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d':' -f1)
    port=$(echo $service | cut -d':' -f2)
    
    if kubectl get service $service_name -n microservice-system &> /dev/null; then
        endpoint=$(kubectl get endpoints $service_name -n microservice-system -o jsonpath='{.subsets[0].addresses[0].ip}' 2>/dev/null)
        if [ -n "$endpoint" ]; then
            print_success "$service_name is running (Endpoint: $endpoint:$port)"
        else
            print_warning "$service_name has no ready endpoints"
        fi
    else
        print_error "$service_name not found"
    fi
done

# External Access Information
print_section "External Access Information"

# LoadBalancer Services
api_gateway_ip=$(kubectl get service api-gateway-service -n microservice-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null)
frontend_ip=$(kubectl get service frontend-service -n microservice-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null)

if [ -n "$api_gateway_ip" ] && [ "$api_gateway_ip" != "null" ]; then
    print_success "API Gateway External IP: http://$api_gateway_ip:8080"
else
    print_status "API Gateway: Use 'kubectl port-forward service/api-gateway-service 8080:8080 -n microservice-system'"
fi

if [ -n "$frontend_ip" ] && [ "$frontend_ip" != "null" ]; then
    print_success "Frontend External IP: http://$frontend_ip:3000"
else
    print_status "Frontend: Use 'kubectl port-forward service/frontend-service 3000:3000 -n microservice-system'"
fi

# Ingress Information
if kubectl get ingress microservice-ingress -n microservice-system &> /dev/null; then
    ingress_ip=$(kubectl get ingress microservice-ingress -n microservice-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null)
    if [ -n "$ingress_ip" ] && [ "$ingress_ip" != "null" ]; then
        print_success "Ingress IP: $ingress_ip"
        print_status "Add to /etc/hosts: $ingress_ip workforce-management.local"
        print_status "Add to /etc/hosts: $ingress_ip api.workforce-management.local"
        print_status "Frontend: http://workforce-management.local"
        print_status "API: http://api.workforce-management.local"
    fi
fi

# Recent Events
print_section "Recent Events"
kubectl get events -n microservice-system --sort-by='.lastTimestamp' | tail -10

# Resource Usage Summary
print_section "Resource Usage Summary"
echo "CPU and Memory usage (if metrics server is available):"
kubectl top pods -n microservice-system 2>/dev/null || print_warning "Metrics server not available"

# Quick Commands Reference
print_section "Quick Commands Reference"
echo "View logs:"
echo "  kubectl logs -f deployment/customer-service -n microservice-system"
echo ""
echo "Port forward services:"
echo "  kubectl port-forward service/frontend-service 3000:3000 -n microservice-system"
echo "  kubectl port-forward service/api-gateway-service 8080:8080 -n microservice-system"
echo ""
echo "Scale services:"
echo "  kubectl scale deployment customer-service --replicas=3 -n microservice-system"
echo ""
echo "Debug connectivity:"
echo "  kubectl exec -it debug-tools -n microservice-system -- curl http://customer-service:8081/actuator/health"
echo ""
echo "Access PostgreSQL:"
echo "  kubectl exec -it postgres-client -n microservice-system -- psql -h postgres-service -U postgres"

print_header "STATUS CHECK COMPLETED"
