#!/bin/bash

# Kubernetes Logs Collection Script for Workforce Management System
# This script helps collect and view logs from all microservices

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

print_section() {
    echo -e "\n${BLUE}--- $1 ---${NC}"
}

print_status() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [SERVICE_NAME]"
    echo ""
    echo "OPTIONS:"
    echo "  -f, --follow     Follow log output (tail -f)"
    echo "  -p, --previous   Show logs from previous container instance"
    echo "  -a, --all        Show logs from all services"
    echo "  -l, --lines N    Show last N lines (default: 100)"
    echo "  -s, --since      Show logs since timestamp (e.g., 1h, 30m, 2006-01-02T15:04:05Z)"
    echo "  -h, --help       Show this help message"
    echo ""
    echo "SERVICE_NAME can be one of:"
    echo "  postgres, api-gateway, customer-service, job-service,"
    echo "  customer-contract-service, customer-payment-service,"
    echo "  customer-statistics-service, frontend"
    echo ""
    echo "Examples:"
    echo "  $0 customer-service                    # Show recent logs"
    echo "  $0 -f api-gateway                      # Follow API Gateway logs"
    echo "  $0 -a                                  # Show logs from all services"
    echo "  $0 -l 50 customer-service              # Show last 50 lines"
    echo "  $0 -s 1h customer-payment-service      # Show logs from last hour"
}

# Default values
FOLLOW=false
PREVIOUS=false
ALL_SERVICES=false
LINES=100
SINCE=""
SERVICE_NAME=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -p|--previous)
            PREVIOUS=true
            shift
            ;;
        -a|--all)
            ALL_SERVICES=true
            shift
            ;;
        -l|--lines)
            LINES="$2"
            shift 2
            ;;
        -s|--since)
            SINCE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            print_error "Unknown option $1"
            show_usage
            exit 1
            ;;
        *)
            SERVICE_NAME="$1"
            shift
            ;;
    esac
done

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
    exit 1
fi

# Check if namespace exists
if ! kubectl get namespace microservice-system &> /dev/null; then
    print_error "Namespace 'microservice-system' does not exist"
    exit 1
fi

# Build kubectl logs command
build_logs_command() {
    local service=$1
    local cmd="kubectl logs"
    
    if [ "$FOLLOW" = true ]; then
        cmd="$cmd -f"
    fi
    
    if [ "$PREVIOUS" = true ]; then
        cmd="$cmd --previous"
    fi
    
    if [ -n "$LINES" ]; then
        cmd="$cmd --tail=$LINES"
    fi
    
    if [ -n "$SINCE" ]; then
        cmd="$cmd --since=$SINCE"
    fi
    
    cmd="$cmd deployment/$service -n microservice-system"
    echo "$cmd"
}

# Function to show logs for a specific service
show_service_logs() {
    local service=$1
    
    # Check if deployment exists
    if ! kubectl get deployment $service -n microservice-system &> /dev/null; then
        print_error "Deployment '$service' not found in namespace 'microservice-system'"
        return 1
    fi
    
    print_section "Logs for $service"
    
    local cmd=$(build_logs_command $service)
    print_status "Executing: $cmd"
    echo ""
    
    eval $cmd
}

# Function to show logs for all services
show_all_logs() {
    local services=("postgres" "customer-service" "job-service" "customer-contract-service" "customer-payment-service" "customer-statistics-service" "api-gateway" "frontend")
    
    for service in "${services[@]}"; do
        if kubectl get deployment $service -n microservice-system &> /dev/null; then
            print_section "Recent logs for $service"
            
            # For all services, show limited lines and don't follow
            local cmd="kubectl logs --tail=20 deployment/$service -n microservice-system"
            if [ -n "$SINCE" ]; then
                cmd="$cmd --since=$SINCE"
            fi
            
            eval $cmd
            echo ""
        else
            print_warning "Deployment '$service' not found"
        fi
    done
}

# Main logic
print_header "WORKFORCE MANAGEMENT SYSTEM - LOGS VIEWER"

if [ "$ALL_SERVICES" = true ]; then
    show_all_logs
elif [ -n "$SERVICE_NAME" ]; then
    show_service_logs "$SERVICE_NAME"
else
    print_error "Please specify a service name or use -a for all services"
    echo ""
    show_usage
    exit 1
fi

print_header "LOGS COLLECTION COMPLETED"
