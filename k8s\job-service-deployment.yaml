apiVersion: apps/v1
kind: Deployment
metadata:
  name: job-service
  namespace: microservice-system
  labels:
    app: job-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: job-service
  template:
    metadata:
      labels:
        app: job-service
    spec:
      containers:
      - name: job-service
        image: job-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8082
          name: http
        envFrom:
        - configMapRef:
            name: job-service-config
        env:
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8082
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8082
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      initContainers:
      - name: wait-for-postgres
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z postgres-service 5432; do echo waiting for postgres; sleep 2; done;']
---
apiVersion: v1
kind: Service
metadata:
  name: job-service
  namespace: microservice-system
  labels:
    app: job-service
spec:
  type: ClusterIP
  ports:
  - port: 8082
    targetPort: 8082
    protocol: TCP
    name: http
  selector:
    app: job-service
