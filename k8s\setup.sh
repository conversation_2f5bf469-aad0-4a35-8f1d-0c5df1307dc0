#!/bin/bash

# Kubernetes Setup Script for Workforce Management System
# This script prepares the environment and makes all scripts executable

set -e

echo "🔧 Setting up Kubernetes deployment environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}========================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}========================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header "KUBERNETES SETUP FOR WORKFORCE MANAGEMENT SYSTEM"

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_status "Script directory: $SCRIPT_DIR"
print_status "Project root: $PROJECT_ROOT"

# Make all shell scripts executable
print_status "Making shell scripts executable..."

chmod +x "$SCRIPT_DIR/build-images.sh"
chmod +x "$SCRIPT_DIR/deploy.sh"
chmod +x "$SCRIPT_DIR/undeploy.sh"
chmod +x "$SCRIPT_DIR/status.sh"
chmod +x "$SCRIPT_DIR/logs.sh"
chmod +x "$SCRIPT_DIR/setup.sh"

print_success "All scripts are now executable"

# Check prerequisites
print_status "Checking prerequisites..."

# Check kubectl
if command -v kubectl &> /dev/null; then
    kubectl_version=$(kubectl version --client --short 2>/dev/null | cut -d' ' -f3)
    print_success "kubectl is installed (version: $kubectl_version)"
else
    print_error "kubectl is not installed"
    echo "Please install kubectl: https://kubernetes.io/docs/tasks/tools/"
fi

# Check Docker
if command -v docker &> /dev/null; then
    docker_version=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    print_success "Docker is installed (version: $docker_version)"
    
    # Check if Docker daemon is running
    if docker info &> /dev/null; then
        print_success "Docker daemon is running"
    else
        print_warning "Docker daemon is not running"
    fi
else
    print_error "Docker is not installed"
    echo "Please install Docker: https://docs.docker.com/get-docker/"
fi

# Check Maven
if command -v mvn &> /dev/null; then
    mvn_version=$(mvn --version | head -n1 | cut -d' ' -f3)
    print_success "Maven is installed (version: $mvn_version)"
else
    print_warning "Maven is not installed"
    echo "Maven is required to build Spring Boot applications"
fi

# Check Node.js and npm
if command -v node &> /dev/null; then
    node_version=$(node --version)
    print_success "Node.js is installed (version: $node_version)"
    
    if command -v npm &> /dev/null; then
        npm_version=$(npm --version)
        print_success "npm is installed (version: $npm_version)"
    else
        print_warning "npm is not installed"
    fi
else
    print_warning "Node.js is not installed"
    echo "Node.js and npm are required to build the React frontend"
fi

# Check Kubernetes cluster connectivity
print_status "Checking Kubernetes cluster connectivity..."
if kubectl cluster-info &> /dev/null; then
    cluster_info=$(kubectl cluster-info | head -n1)
    print_success "Connected to Kubernetes cluster"
    print_status "$cluster_info"
    
    # Check cluster nodes
    node_count=$(kubectl get nodes --no-headers | wc -l)
    print_status "Cluster has $node_count node(s)"
    
    # Check if metrics server is available
    if kubectl top nodes &> /dev/null; then
        print_success "Metrics server is available (HPA will work)"
    else
        print_warning "Metrics server is not available (HPA will not work)"
    fi
    
    # Check for Ingress controller
    if kubectl get ingressclass &> /dev/null; then
        ingress_classes=$(kubectl get ingressclass --no-headers | wc -l)
        if [ $ingress_classes -gt 0 ]; then
            print_success "Ingress controller is available"
            kubectl get ingressclass --no-headers | while read line; do
                class_name=$(echo $line | cut -d' ' -f1)
                print_status "  - $class_name"
            done
        else
            print_warning "No Ingress classes found"
        fi
    else
        print_warning "Ingress controller is not available"
    fi
    
else
    print_error "Cannot connect to Kubernetes cluster"
    echo "Please check your kubeconfig and cluster status"
fi

# Check if namespace already exists
if kubectl get namespace microservice-system &> /dev/null; then
    print_warning "Namespace 'microservice-system' already exists"
    print_status "Existing resources:"
    kubectl get all -n microservice-system 2>/dev/null | head -10
else
    print_status "Namespace 'microservice-system' does not exist (will be created during deployment)"
fi

# Validate YAML files
print_status "Validating Kubernetes YAML files..."

yaml_files=(
    "namespace.yaml"
    "secrets.yaml"
    "configmaps.yaml"
    "postgres-storage.yaml"
    "postgres-deployment.yaml"
    "api-gateway-deployment.yaml"
    "customer-service-deployment.yaml"
    "job-service-deployment.yaml"
    "customer-contract-service-deployment.yaml"
    "customer-payment-service-deployment.yaml"
    "customer-statistics-service-deployment.yaml"
    "frontend-deployment.yaml"
    "ingress.yaml"
    "hpa.yaml"
    "network-policy.yaml"
)

valid_files=0
total_files=${#yaml_files[@]}

for yaml_file in "${yaml_files[@]}"; do
    if [ -f "$SCRIPT_DIR/$yaml_file" ]; then
        if kubectl apply --dry-run=client -f "$SCRIPT_DIR/$yaml_file" &> /dev/null; then
            valid_files=$((valid_files + 1))
        else
            print_warning "YAML validation failed for $yaml_file"
        fi
    else
        print_error "File not found: $yaml_file"
    fi
done

print_status "YAML validation: $valid_files/$total_files files are valid"

# Check if JAR files exist
print_status "Checking if Spring Boot applications are built..."

services=("api-gateway" "customer-service" "job-service" "customer-contract-service" "customer-payment-service" "customer-statistics-service")
built_services=0

for service in "${services[@]}"; do
    jar_file="$PROJECT_ROOT/$service/target/${service}-0.0.1-SNAPSHOT.jar"
    if [ -f "$jar_file" ]; then
        built_services=$((built_services + 1))
        print_success "$service is built"
    else
        print_warning "$service is not built (JAR file not found)"
    fi
done

print_status "Built services: $built_services/${#services[@]}"

if [ $built_services -lt ${#services[@]} ]; then
    print_warning "Some services are not built. Run 'mvn clean package' in each service directory"
fi

# Check if frontend is built
if [ -d "$PROJECT_ROOT/microservice_fe/build" ]; then
    print_success "Frontend is built"
else
    print_warning "Frontend is not built. Run 'npm run build' in microservice_fe directory"
fi

# Display next steps
print_header "SETUP COMPLETED"

echo ""
print_status "Next steps:"
echo ""
echo "1. Build all applications (if not already built):"
echo "   cd $PROJECT_ROOT"
echo "   ./k8s/build-images.sh"
echo ""
echo "2. Deploy to Kubernetes:"
echo "   ./k8s/deploy.sh"
echo ""
echo "3. Check deployment status:"
echo "   ./k8s/status.sh"
echo ""
echo "4. View logs:"
echo "   ./k8s/logs.sh -a"
echo ""
echo "5. Clean up (when needed):"
echo "   ./k8s/undeploy.sh"
echo ""

print_success "🎉 Setup completed! You can now proceed with the deployment."
