apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: microservice-ingress
  namespace: microservice-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: workforce-management.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 3000
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 8080
  - host: api.workforce-management.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: api-gateway-service
            port:
              number: 8080
---
# Alternative Ingress for direct service access (for development/debugging)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: microservice-dev-ingress
  namespace: microservice-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: dev.workforce-management.local
    http:
      paths:
      - path: /customer-service(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: customer-service
            port:
              number: 8081
      - path: /job-service(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: job-service
            port:
              number: 8082
      - path: /customer-contract-service(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: customer-contract-service
            port:
              number: 8083
      - path: /customer-payment-service(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: customer-payment-service
            port:
              number: 8084
      - path: /customer-statistics-service(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: customer-statistics-service
            port:
              number: 8085
