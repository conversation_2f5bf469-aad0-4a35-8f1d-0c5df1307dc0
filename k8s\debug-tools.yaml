apiVersion: v1
kind: Pod
metadata:
  name: debug-tools
  namespace: microservice-system
  labels:
    app: debug-tools
spec:
  containers:
  - name: debug-tools
    image: nicolaka/netshoot:latest
    command: ["/bin/bash"]
    args: ["-c", "while true; do sleep 30; done;"]
    resources:
      requests:
        memory: "64Mi"
        cpu: "50m"
      limits:
        memory: "128Mi"
        cpu: "100m"
  restartPolicy: Always
---
apiVersion: v1
kind: Pod
metadata:
  name: postgres-client
  namespace: microservice-system
  labels:
    app: postgres-client
spec:
  containers:
  - name: postgres-client
    image: postgres:15-alpine
    command: ["/bin/sh"]
    args: ["-c", "while true; do sleep 30; done;"]
    env:
    - name: PGPASSWORD
      valueFrom:
        secretKeyRef:
          name: postgres-secret
          key: password
    - name: PGUSER
      valueFrom:
        secretKeyRef:
          name: postgres-secret
          key: username
    - name: PGHOST
      value: "postgres-service"
    - name: PGPORT
      value: "5432"
    resources:
      requests:
        memory: "64Mi"
        cpu: "50m"
      limits:
        memory: "128Mi"
        cpu: "100m"
  restartPolicy: Always
