#!/bin/bash

# Kubernetes Undeployment Script for Workforce Management Microservices
# This script removes the entire microservices architecture from Kubernetes

set -e

echo "🗑️  Starting Kubernetes undeployment for Workforce Management System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
    exit 1
fi

print_success "Connected to Kubernetes cluster"

# Check if namespace exists
if ! kubectl get namespace microservice-system &> /dev/null; then
    print_warning "Namespace 'microservice-system' does not exist. Nothing to undeploy."
    exit 0
fi

print_status "Removing Kubernetes resources..."

# Remove in reverse order of deployment

# Step 1: Remove Network Policies
print_status "Removing Network Policies..."
kubectl delete -f network-policy.yaml --ignore-not-found=true
print_success "Network Policies removed"

# Step 2: Remove HPA
print_status "Removing Horizontal Pod Autoscalers..."
kubectl delete -f hpa.yaml --ignore-not-found=true
print_success "HPA removed"

# Step 3: Remove Ingress
print_status "Removing Ingress..."
kubectl delete -f ingress.yaml --ignore-not-found=true
print_success "Ingress removed"

# Step 4: Remove Frontend
print_status "Removing Frontend..."
kubectl delete -f frontend-deployment.yaml --ignore-not-found=true
print_success "Frontend removed"

# Step 5: Remove API Gateway
print_status "Removing API Gateway..."
kubectl delete -f api-gateway-deployment.yaml --ignore-not-found=true
print_success "API Gateway removed"

# Step 6: Remove microservices
print_status "Removing microservices..."
kubectl delete -f customer-statistics-service-deployment.yaml --ignore-not-found=true
kubectl delete -f customer-payment-service-deployment.yaml --ignore-not-found=true
kubectl delete -f customer-contract-service-deployment.yaml --ignore-not-found=true
kubectl delete -f job-service-deployment.yaml --ignore-not-found=true
kubectl delete -f customer-service-deployment.yaml --ignore-not-found=true
print_success "Microservices removed"

# Step 7: Remove PostgreSQL
print_status "Removing PostgreSQL..."
kubectl delete -f postgres-deployment.yaml --ignore-not-found=true
print_success "PostgreSQL removed"

# Step 8: Remove storage resources
print_status "Removing storage resources..."
kubectl delete -f postgres-storage.yaml --ignore-not-found=true
print_success "Storage resources removed"

# Step 9: Remove configmaps
print_status "Removing ConfigMaps..."
kubectl delete -f configmaps.yaml --ignore-not-found=true
print_success "ConfigMaps removed"

# Step 10: Remove secrets
print_status "Removing Secrets..."
kubectl delete -f secrets.yaml --ignore-not-found=true
print_success "Secrets removed"

# Step 11: Remove namespace (optional)
read -p "Do you want to remove the namespace 'microservice-system'? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Removing namespace..."
    kubectl delete -f namespace.yaml --ignore-not-found=true
    print_success "Namespace removed"
else
    print_warning "Namespace 'microservice-system' preserved"
fi

print_success "🎉 Undeployment completed successfully!"

# Show remaining resources (if any)
if kubectl get namespace microservice-system &> /dev/null; then
    print_status "Remaining resources in microservice-system namespace:"
    kubectl get all -n microservice-system
fi
