apiVersion: v1
kind: ServiceMonitor
metadata:
  name: microservices-monitor
  namespace: microservice-system
  labels:
    app: microservices
spec:
  selector:
    matchLabels:
      monitoring: enabled
  endpoints:
  - port: http
    path: /actuator/prometheus
    interval: 30s
---
apiVersion: v1
kind: Service
metadata:
  name: customer-service-monitoring
  namespace: microservice-system
  labels:
    app: customer-service
    monitoring: enabled
spec:
  type: ClusterIP
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: http
  selector:
    app: customer-service
---
apiVersion: v1
kind: Service
metadata:
  name: job-service-monitoring
  namespace: microservice-system
  labels:
    app: job-service
    monitoring: enabled
spec:
  type: ClusterIP
  ports:
  - port: 8082
    targetPort: 8082
    protocol: TCP
    name: http
  selector:
    app: job-service
---
apiVersion: v1
kind: Service
metadata:
  name: customer-contract-service-monitoring
  namespace: microservice-system
  labels:
    app: customer-contract-service
    monitoring: enabled
spec:
  type: ClusterIP
  ports:
  - port: 8083
    targetPort: 8083
    protocol: TCP
    name: http
  selector:
    app: customer-contract-service
---
apiVersion: v1
kind: Service
metadata:
  name: customer-payment-service-monitoring
  namespace: microservice-system
  labels:
    app: customer-payment-service
    monitoring: enabled
spec:
  type: ClusterIP
  ports:
  - port: 8084
    targetPort: 8084
    protocol: TCP
    name: http
  selector:
    app: customer-payment-service
---
apiVersion: v1
kind: Service
metadata:
  name: customer-statistics-service-monitoring
  namespace: microservice-system
  labels:
    app: customer-statistics-service
    monitoring: enabled
spec:
  type: ClusterIP
  ports:
  - port: 8085
    targetPort: 8085
    protocol: TCP
    name: http
  selector:
    app: customer-statistics-service
---
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-monitoring
  namespace: microservice-system
  labels:
    app: api-gateway
    monitoring: enabled
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: api-gateway
