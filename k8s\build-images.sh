#!/bin/bash

# Docker Image Build Script for Workforce Management Microservices
# This script builds all Docker images required for Kubernetes deployment

set -e

echo "🐳 Building Docker images for Workforce Management System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if Docker daemon is running
if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

print_success "Docker is available and running"

# Navigate to project root
cd "$(dirname "$0")/.."

# Build Spring Boot microservices
print_status "Building Spring Boot microservices..."

services=("api-gateway" "customer-service" "job-service" "customer-contract-service" "customer-payment-service" "customer-statistics-service")

for service in "${services[@]}"; do
    print_status "Building $service..."
    
    if [ ! -d "$service" ]; then
        print_error "Directory $service not found"
        continue
    fi
    
    cd "$service"
    
    # Check if JAR file exists
    jar_file="target/${service}-0.0.1-SNAPSHOT.jar"
    if [ ! -f "$jar_file" ]; then
        print_warning "JAR file not found for $service. Building with Maven..."
        if command -v mvn &> /dev/null; then
            mvn clean package -DskipTests
            if [ $? -ne 0 ]; then
                print_error "Maven build failed for $service"
                cd ..
                continue
            fi
        else
            print_error "Maven not found. Please build $service manually with 'mvn clean package'"
            cd ..
            continue
        fi
    fi
    
    # Build Docker image
    if [ -f "Dockerfile" ]; then
        docker build -t "$service:latest" .
        if [ $? -eq 0 ]; then
            print_success "Built $service:latest"
        else
            print_error "Failed to build $service:latest"
        fi
    else
        print_error "Dockerfile not found for $service"
    fi
    
    cd ..
done

# Build React frontend
print_status "Building React frontend..."

if [ ! -d "microservice_fe" ]; then
    print_error "Frontend directory 'microservice_fe' not found"
    exit 1
fi

cd microservice_fe

# Check if build directory exists
if [ ! -d "build" ]; then
    print_warning "Frontend build directory not found. Building with npm..."
    if command -v npm &> /dev/null; then
        npm install
        npm run build
        if [ $? -ne 0 ]; then
            print_error "npm build failed for frontend"
            cd ..
            exit 1
        fi
    else
        print_error "npm not found. Please build frontend manually with 'npm run build'"
        cd ..
        exit 1
    fi
fi

# Build Docker image
if [ -f "Dockerfile" ]; then
    docker build -t "frontend:latest" .
    if [ $? -eq 0 ]; then
        print_success "Built frontend:latest"
    else
        print_error "Failed to build frontend:latest"
    fi
else
    print_error "Dockerfile not found for frontend"
fi

cd ..

# List built images
print_status "Built Docker images:"
echo ""
docker images | grep -E "(api-gateway|customer-service|job-service|customer-contract-service|customer-payment-service|customer-statistics-service|frontend)" | grep latest

print_success "🎉 All Docker images built successfully!"
print_status "You can now run './k8s/deploy.sh' to deploy to Kubernetes"
