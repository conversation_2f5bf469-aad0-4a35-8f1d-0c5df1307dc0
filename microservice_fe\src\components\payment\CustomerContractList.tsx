import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Card,
  CardContent,
  CardActions,
  Divider,
  useTheme,
  useMediaQuery,
  Alert,
} from '@mui/material';
import PaymentIcon from '@mui/icons-material/Payment';
import { CustomerContract } from '../../models';
import { formatCurrency } from '../../utils/formatters';
import { formatDateLocalized } from '../../utils/dateUtils';

interface CustomerContractListProps {
  contracts: CustomerContract[];
  onPaymentClick: (contract: CustomerContract) => void;
}

const CustomerContractList = ({
  contracts,
  onPaymentClick,
}: CustomerContractListProps): React.ReactElement => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));



  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return formatDateLocalized(dateString);
  };

  if (contracts.length === 0) {
    return (
      <Alert severity="info" sx={{ mb: 3 }}>
        Khách hàng này chưa có hợp đồng nào cần thanh toán.
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Danh sách hợp đồng cần thanh toán
      </Typography>

      {isMobile ? (
        // Mobile view - card list
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {contracts.map((contract) => {
            const totalPaid = contract.totalPaid || 0;
            const remaining = contract.totalAmount - totalPaid;

            return (
              <Card variant="outlined" key={contract.id}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" component="div">
                      #{contract.id}
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 1 }} />

                  <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1 }}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Ngày bắt đầu
                      </Typography>
                      <Typography variant="body1">
                        {formatDate(contract.startingDate)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Ngày kết thúc
                      </Typography>
                      <Typography variant="body1">
                        {formatDate(contract.endingDate)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Tổng giá trị
                      </Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {formatCurrency(contract.totalAmount)}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Đã thanh toán
                      </Typography>
                      <Typography variant="body1">
                        {formatCurrency(totalPaid)}
                      </Typography>
                    </Box>
                  </Box>

                  <Box sx={{ mt: 2, p: 1, bgcolor: 'primary.light', borderRadius: 1 }}>
                    <Typography variant="body2" color="primary.contrastText">
                      Còn lại cần thanh toán
                    </Typography>
                    <Typography variant="h6" color="primary.contrastText" fontWeight="bold">
                      {formatCurrency(remaining)}
                    </Typography>
                  </Box>
                </CardContent>

                <CardActions>
                  <Button
                    fullWidth
                    variant="contained"
                    color="primary"
                    startIcon={<PaymentIcon />}
                    onClick={() => onPaymentClick(contract)}
                    disabled={remaining <= 0}
                  >
                    Thanh toán
                  </Button>
                </CardActions>
              </Card>
            );
          })}
        </Box>
      ) : (
        // Desktop view - table
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'primary.light' }}>
                <TableCell>Mã hợp đồng</TableCell>
                <TableCell>Ngày bắt đầu</TableCell>
                <TableCell>Ngày kết thúc</TableCell>
                <TableCell align="right">Tổng giá trị</TableCell>
                <TableCell align="right">Đã thanh toán</TableCell>
                <TableCell align="right">Còn lại</TableCell>

                <TableCell align="center">Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {contracts.map((contract) => {
                const totalPaid = contract.totalPaid || 0;
                const remaining = contract.totalAmount - totalPaid;

                return (
                  <TableRow key={contract.id} hover>
                    <TableCell>#{contract.id}</TableCell>
                    <TableCell>{formatDate(contract.startingDate)}</TableCell>
                    <TableCell>{formatDate(contract.endingDate)}</TableCell>
                    <TableCell align="right">{formatCurrency(contract.totalAmount)}</TableCell>
                    <TableCell align="right">{formatCurrency(totalPaid)}</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                      {formatCurrency(remaining)}
                    </TableCell>

                    <TableCell align="center">
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        startIcon={<PaymentIcon />}
                        onClick={() => onPaymentClick(contract)}
                        disabled={remaining <= 0}
                      >
                        Thanh toán
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default CustomerContractList;
