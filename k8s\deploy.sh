#!/bin/bash

# Kubernetes Deployment Script for Workforce Management Microservices
# This script deploys the entire microservices architecture to Kubernetes

set -e

echo "🚀 Starting Kubernetes deployment for Workforce Management System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to Kubernetes cluster
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
    exit 1
fi

print_success "Connected to Kubernetes cluster"

# Build Docker images first
print_status "Building Docker images..."
cd ..

# Build all microservice images
services=("api-gateway" "customer-service" "job-service" "customer-contract-service" "customer-payment-service" "customer-statistics-service")

for service in "${services[@]}"; do
    print_status "Building $service..."
    cd $service
    if [ -f "target/${service}-0.0.1-SNAPSHOT.jar" ]; then
        docker build -t $service:latest .
        print_success "Built $service:latest"
    else
        print_error "JAR file not found for $service. Please run 'mvn clean package' first."
        exit 1
    fi
    cd ..
done

# Build frontend image
print_status "Building frontend..."
cd microservice_fe
if [ -d "build" ]; then
    docker build -t frontend:latest .
    print_success "Built frontend:latest"
else
    print_error "Frontend build directory not found. Please run 'npm run build' first."
    exit 1
fi
cd ..

# Return to k8s directory
cd k8s

# Deploy to Kubernetes
print_status "Deploying to Kubernetes..."

# Step 1: Create namespace
print_status "Creating namespace..."
kubectl apply -f namespace.yaml
print_success "Namespace created"

# Step 2: Create secrets
print_status "Creating secrets..."
kubectl apply -f secrets.yaml
print_success "Secrets created"

# Step 3: Create configmaps
print_status "Creating configmaps..."
kubectl apply -f configmaps.yaml
print_success "ConfigMaps created"

# Step 4: Create storage resources
print_status "Creating storage resources..."
kubectl apply -f postgres-storage.yaml
print_success "Storage resources created"

# Step 5: Deploy PostgreSQL
print_status "Deploying PostgreSQL..."
kubectl apply -f postgres-deployment.yaml
print_success "PostgreSQL deployed"

# Wait for PostgreSQL to be ready
print_status "Waiting for PostgreSQL to be ready..."
kubectl wait --for=condition=ready pod -l app=postgres -n microservice-system --timeout=300s
print_success "PostgreSQL is ready"

# Step 6: Deploy microservices in order
print_status "Deploying microservices..."

# Deploy base services first
kubectl apply -f customer-service-deployment.yaml
kubectl apply -f job-service-deployment.yaml

# Wait for base services
print_status "Waiting for base services to be ready..."
kubectl wait --for=condition=ready pod -l app=customer-service -n microservice-system --timeout=300s
kubectl wait --for=condition=ready pod -l app=job-service -n microservice-system --timeout=300s
print_success "Base services are ready"

# Deploy dependent services
kubectl apply -f customer-contract-service-deployment.yaml
kubectl apply -f customer-payment-service-deployment.yaml
kubectl apply -f customer-statistics-service-deployment.yaml

# Wait for dependent services
print_status "Waiting for dependent services to be ready..."
kubectl wait --for=condition=ready pod -l app=customer-contract-service -n microservice-system --timeout=300s
kubectl wait --for=condition=ready pod -l app=customer-payment-service -n microservice-system --timeout=300s
kubectl wait --for=condition=ready pod -l app=customer-statistics-service -n microservice-system --timeout=300s
print_success "Dependent services are ready"

# Deploy API Gateway
kubectl apply -f api-gateway-deployment.yaml

# Wait for API Gateway
print_status "Waiting for API Gateway to be ready..."
kubectl wait --for=condition=ready pod -l app=api-gateway -n microservice-system --timeout=300s
print_success "API Gateway is ready"

# Deploy Frontend
kubectl apply -f frontend-deployment.yaml

# Wait for Frontend
print_status "Waiting for Frontend to be ready..."
kubectl wait --for=condition=ready pod -l app=frontend -n microservice-system --timeout=300s
print_success "Frontend is ready"

# Step 7: Deploy Ingress (optional)
if kubectl get ingressclass nginx &> /dev/null; then
    print_status "Deploying Ingress..."
    kubectl apply -f ingress.yaml
    print_success "Ingress deployed"
else
    print_warning "NGINX Ingress Controller not found. Skipping Ingress deployment."
    print_warning "You can access services via LoadBalancer or NodePort."
fi

# Step 8: Deploy HPA (optional)
if kubectl top nodes &> /dev/null; then
    print_status "Deploying Horizontal Pod Autoscalers..."
    kubectl apply -f hpa.yaml
    print_success "HPA deployed"
else
    print_warning "Metrics server not available. Skipping HPA deployment."
fi

# Step 9: Deploy Network Policies (optional)
print_status "Deploying Network Policies..."
kubectl apply -f network-policy.yaml
print_success "Network Policies deployed"

# Display deployment status
print_status "Deployment Summary:"
echo ""
kubectl get pods -n microservice-system
echo ""
kubectl get services -n microservice-system
echo ""

# Get external access information
print_status "External Access Information:"
API_GATEWAY_IP=$(kubectl get service api-gateway-service -n microservice-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "Pending")
FRONTEND_IP=$(kubectl get service frontend-service -n microservice-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "Pending")

if [ "$API_GATEWAY_IP" != "Pending" ] && [ "$API_GATEWAY_IP" != "" ]; then
    echo "API Gateway: http://$API_GATEWAY_IP:8080"
else
    echo "API Gateway: Use 'kubectl port-forward service/api-gateway-service 8080:8080 -n microservice-system'"
fi

if [ "$FRONTEND_IP" != "Pending" ] && [ "$FRONTEND_IP" != "" ]; then
    echo "Frontend: http://$FRONTEND_IP:3000"
else
    echo "Frontend: Use 'kubectl port-forward service/frontend-service 3000:3000 -n microservice-system'"
fi

print_success "🎉 Deployment completed successfully!"
print_status "Use 'kubectl get all -n microservice-system' to check the status of all resources."
