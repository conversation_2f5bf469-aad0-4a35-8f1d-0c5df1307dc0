apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: microservice-system
data:
  POSTGRES_DB: postgres
  POSTGRES_USER: postgres
  PGDATA: /var/lib/postgresql/data/pgdata
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: api-gateway-config
  namespace: microservice-system
data:
  SPRING_PROFILES_ACTIVE: "kubernetes"
  SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING: "true"
  SERVER_PORT: "8080"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: customer-service-config
  namespace: microservice-system
data:
  SPRING_PROFILES_ACTIVE: "kubernetes"
  SPRING_DATASOURCE_URL: "**************************************************"
  SPRING_DATASOURCE_USERNAME: "postgres"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "validate"
  SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING: "true"
  SPRING_SQL_INIT_MODE: "never"
  SERVER_PORT: "8081"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: job-service-config
  namespace: microservice-system
data:
  SPRING_PROFILES_ACTIVE: "kubernetes"
  SPRING_DATASOURCE_URL: "*********************************************"
  SPRING_DATASOURCE_USERNAME: "postgres"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "validate"
  SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING: "true"
  SPRING_SQL_INIT_MODE: "never"
  SERVER_PORT: "8082"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: customer-contract-service-config
  namespace: microservice-system
data:
  SPRING_PROFILES_ACTIVE: "kubernetes"
  SPRING_DATASOURCE_URL: "**********************************************************"
  SPRING_DATASOURCE_USERNAME: "postgres"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "validate"
  SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING: "true"
  SPRING_SQL_INIT_MODE: "never"
  SERVER_PORT: "8083"
  CUSTOMER_SERVICE_URL: "http://customer-service:8081/api/customer"
  JOB_SERVICE_URL: "http://job-service:8082/api/job"
  JOB_CATEGORY_SERVICE_URL: "http://job-service:8082/api/job-category"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: customer-payment-service-config
  namespace: microservice-system
data:
  SPRING_PROFILES_ACTIVE: "kubernetes"
  SPRING_DATASOURCE_URL: "*********************************************************"
  SPRING_DATASOURCE_USERNAME: "postgres"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "validate"
  SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING: "true"
  SPRING_SQL_INIT_MODE: "never"
  SERVER_PORT: "8084"
  CUSTOMER_SERVICE_URL: "http://customer-service:8081/api/customer"
  CUSTOMERCONTRACT_SERVICE_URL: "http://customer-contract-service:8083/api/customer-contract"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: customer-statistics-service-config
  namespace: microservice-system
data:
  SPRING_PROFILES_ACTIVE: "kubernetes"
  SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING: "true"
  SERVER_PORT: "8085"
  APP_CUSTOMER_SERVICE_URL: "http://customer-service:8081/api/customer"
  APP_CUSTOMER_CONTRACT_SERVICE_URL: "http://customer-contract-service:8083/api/customer-contract"
  APP_CUSTOMER_PAYMENT_SERVICE_URL: "http://customer-payment-service:8084/api/customer-payment"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-config
  namespace: microservice-system
data:
  REACT_APP_API_URL: "http://api-gateway-service:8080"
  PORT: "3000"
