apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-statistics-service
  namespace: microservice-system
  labels:
    app: customer-statistics-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: customer-statistics-service
  template:
    metadata:
      labels:
        app: customer-statistics-service
    spec:
      containers:
      - name: customer-statistics-service
        image: customer-statistics-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8085
          name: http
        envFrom:
        - configMapRef:
            name: customer-statistics-service-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8085
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      initContainers:
      - name: wait-for-customer-service
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z customer-service 8081; do echo waiting for customer-service; sleep 2; done;']
      - name: wait-for-customer-contract-service
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z customer-contract-service 8083; do echo waiting for customer-contract-service; sleep 2; done;']
      - name: wait-for-customer-payment-service
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z customer-payment-service 8084; do echo waiting for customer-payment-service; sleep 2; done;']
---
apiVersion: v1
kind: Service
metadata:
  name: customer-statistics-service
  namespace: microservice-system
  labels:
    app: customer-statistics-service
spec:
  type: ClusterIP
  ports:
  - port: 8085
    targetPort: 8085
    protocol: TCP
    name: http
  selector:
    app: customer-statistics-service
