# Default values for workforce-management
# This is a YAML-formatted file.

# Global settings
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

# Namespace
namespace: microservice-system

# PostgreSQL configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: "1234"
    username: "postgres"
    password: "1234"
    database: "postgres"
  primary:
    persistence:
      enabled: true
      size: 10Gi
    resources:
      requests:
        memory: 512Mi
        cpu: 250m
      limits:
        memory: 1Gi
        cpu: 500m

# API Gateway
apiGateway:
  enabled: true
  image:
    repository: api-gateway
    tag: latest
    pullPolicy: IfNotPresent
  replicaCount: 2
  service:
    type: LoadBalancer
    port: 8080
  resources:
    requests:
      memory: 512Mi
      cpu: 250m
    limits:
      memory: 1Gi
      cpu: 500m
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Customer Service
customerService:
  enabled: true
  image:
    repository: customer-service
    tag: latest
    pullPolicy: IfNotPresent
  replicaCount: 2
  service:
    type: ClusterIP
    port: 8081
  resources:
    requests:
      memory: 512Mi
      cpu: 250m
    limits:
      memory: 1Gi
      cpu: 500m
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Job Service
jobService:
  enabled: true
  image:
    repository: job-service
    tag: latest
    pullPolicy: IfNotPresent
  replicaCount: 2
  service:
    type: ClusterIP
    port: 8082
  resources:
    requests:
      memory: 512Mi
      cpu: 250m
    limits:
      memory: 1Gi
      cpu: 500m
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Customer Contract Service
customerContractService:
  enabled: true
  image:
    repository: customer-contract-service
    tag: latest
    pullPolicy: IfNotPresent
  replicaCount: 2
  service:
    type: ClusterIP
    port: 8083
  resources:
    requests:
      memory: 512Mi
      cpu: 250m
    limits:
      memory: 1Gi
      cpu: 500m
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Customer Payment Service
customerPaymentService:
  enabled: true
  image:
    repository: customer-payment-service
    tag: latest
    pullPolicy: IfNotPresent
  replicaCount: 2
  service:
    type: ClusterIP
    port: 8084
  resources:
    requests:
      memory: 512Mi
      cpu: 250m
    limits:
      memory: 1Gi
      cpu: 500m
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Customer Statistics Service
customerStatisticsService:
  enabled: true
  image:
    repository: customer-statistics-service
    tag: latest
    pullPolicy: IfNotPresent
  replicaCount: 2
  service:
    type: ClusterIP
    port: 8085
  resources:
    requests:
      memory: 512Mi
      cpu: 250m
    limits:
      memory: 1Gi
      cpu: 500m
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 6
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Frontend
frontend:
  enabled: true
  image:
    repository: frontend
    tag: latest
    pullPolicy: IfNotPresent
  replicaCount: 2
  service:
    type: LoadBalancer
    port: 3000
  resources:
    requests:
      memory: 256Mi
      cpu: 100m
    limits:
      memory: 512Mi
      cpu: 250m
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 6
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# Ingress
ingress:
  enabled: false
  className: nginx
  annotations: {}
  hosts:
    - host: workforce-management.local
      paths:
        - path: /
          pathType: Prefix
          service: frontend
        - path: /api
          pathType: Prefix
          service: api-gateway
  tls: []

# Network Policies
networkPolicies:
  enabled: true

# Monitoring
monitoring:
  enabled: false
  serviceMonitor:
    enabled: false

# Security Context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  fsGroup: 2000

# Pod Security Context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 1000

# Service Account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Node Selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}
