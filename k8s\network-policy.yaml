apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: postgres-network-policy
  namespace: microservice-system
spec:
  podSelector:
    matchLabels:
      app: postgres
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: customer-service
    - podSelector:
        matchLabels:
          app: job-service
    - podSelector:
        matchLabels:
          app: customer-contract-service
    - podSelector:
        matchLabels:
          app: customer-payment-service
    ports:
    - protocol: TCP
      port: 5432
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-gateway-network-policy
  namespace: microservice-system
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []  # Allow all ingress traffic to API Gateway
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: customer-service
    ports:
    - protocol: TCP
      port: 8081
  - to:
    - podSelector:
        matchLabels:
          app: job-service
    ports:
    - protocol: TCP
      port: 8082
  - to:
    - podSelector:
        matchLabels:
          app: customer-contract-service
    ports:
    - protocol: TCP
      port: 8083
  - to:
    - podSelector:
        matchLabels:
          app: customer-payment-service
    ports:
    - protocol: TCP
      port: 8084
  - to:
    - podSelector:
        matchLabels:
          app: customer-statistics-service
    ports:
    - protocol: TCP
      port: 8085
  - to: []  # Allow DNS resolution
    ports:
    - protocol: UDP
      port: 53
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: frontend-network-policy
  namespace: microservice-system
spec:
  podSelector:
    matchLabels:
      app: frontend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []  # Allow all ingress traffic to frontend
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: api-gateway
    ports:
    - protocol: TCP
      port: 8080
  - to: []  # Allow DNS resolution
    ports:
    - protocol: UDP
      port: 53
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: microservices-network-policy
  namespace: microservice-system
spec:
  podSelector:
    matchLabels:
      tier: microservice
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: api-gateway
    - podSelector:
        matchLabels:
          tier: microservice
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: postgres
    ports:
    - protocol: TCP
      port: 5432
  - to:
    - podSelector:
        matchLabels:
          tier: microservice
  - to: []  # Allow DNS resolution
    ports:
    - protocol: UDP
      port: 53
