# PowerShell Setup Script for Kubernetes Deployment
# This script prepares the environment for Kubernetes deployment on Windows

Write-Host "🔧 Setting up Kubernetes deployment environment..." -ForegroundColor Blue

# Function to print colored output
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Cyan
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Host "========================================" -ForegroundColor Magenta
Write-Host "KUBERNETES SETUP FOR WORKFORCE MANAGEMENT SYSTEM" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta

# Get script directory and project root
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

Write-Status "Script directory: $ScriptDir"
Write-Status "Project root: $ProjectRoot"

# Check prerequisites
Write-Status "Checking prerequisites..."

# Check kubectl
if (Get-Command kubectl -ErrorAction SilentlyContinue) {
    $kubectlVersion = (kubectl version --client --short 2>$null) -replace ".*v", "v"
    Write-Success "kubectl is installed (version: $kubectlVersion)"
} else {
    Write-Error "kubectl is not installed"
    Write-Host "Please install kubectl: https://kubernetes.io/docs/tasks/tools/"
}

# Check Docker
if (Get-Command docker -ErrorAction SilentlyContinue) {
    $dockerVersion = (docker --version) -replace "Docker version ", "" -replace ",.*", ""
    Write-Success "Docker is installed (version: $dockerVersion)"
    
    # Check if Docker daemon is running
    try {
        docker info | Out-Null
        Write-Success "Docker daemon is running"
    } catch {
        Write-Warning "Docker daemon is not running"
    }
} else {
    Write-Error "Docker is not installed"
    Write-Host "Please install Docker: https://docs.docker.com/get-docker/"
}

# Check Maven
if (Get-Command mvn -ErrorAction SilentlyContinue) {
    $mvnVersion = (mvn --version | Select-String "Apache Maven") -replace "Apache Maven ", "" -replace " \(.*", ""
    Write-Success "Maven is installed (version: $mvnVersion)"
} else {
    Write-Warning "Maven is not installed"
    Write-Host "Maven is required to build Spring Boot applications"
}

# Check Node.js and npm
if (Get-Command node -ErrorAction SilentlyContinue) {
    $nodeVersion = node --version
    Write-Success "Node.js is installed (version: $nodeVersion)"
    
    if (Get-Command npm -ErrorAction SilentlyContinue) {
        $npmVersion = npm --version
        Write-Success "npm is installed (version: $npmVersion)"
    } else {
        Write-Warning "npm is not installed"
    }
} else {
    Write-Warning "Node.js is not installed"
    Write-Host "Node.js and npm are required to build the React frontend"
}

# Check Kubernetes cluster connectivity
Write-Status "Checking Kubernetes cluster connectivity..."
try {
    $clusterInfo = kubectl cluster-info 2>$null | Select-String "Kubernetes control plane"
    Write-Success "Connected to Kubernetes cluster"
    Write-Status "$clusterInfo"
    
    # Check cluster nodes
    $nodeCount = (kubectl get nodes --no-headers 2>$null | Measure-Object).Count
    Write-Status "Cluster has $nodeCount node(s)"
    
    # Check if metrics server is available
    try {
        kubectl top nodes 2>$null | Out-Null
        Write-Success "Metrics server is available (HPA will work)"
    } catch {
        Write-Warning "Metrics server is not available (HPA will not work)"
    }
    
    # Check for Ingress controller
    try {
        $ingressClasses = kubectl get ingressclass --no-headers 2>$null
        if ($ingressClasses) {
            Write-Success "Ingress controller is available"
            $ingressClasses | ForEach-Object {
                $className = ($_ -split '\s+')[0]
                Write-Status "  - $className"
            }
        } else {
            Write-Warning "No Ingress classes found"
        }
    } catch {
        Write-Warning "Ingress controller is not available"
    }
    
} catch {
    Write-Error "Cannot connect to Kubernetes cluster"
    Write-Host "Please check your kubeconfig and cluster status"
}

# Check if namespace already exists
try {
    kubectl get namespace microservice-system 2>$null | Out-Null
    Write-Warning "Namespace 'microservice-system' already exists"
    Write-Status "Existing resources:"
    kubectl get all -n microservice-system 2>$null | Select-Object -First 10
} catch {
    Write-Status "Namespace 'microservice-system' does not exist (will be created during deployment)"
}

# Validate YAML files
Write-Status "Validating Kubernetes YAML files..."

$yamlFiles = @(
    "namespace.yaml",
    "secrets.yaml",
    "configmaps.yaml",
    "postgres-storage.yaml",
    "postgres-deployment.yaml",
    "api-gateway-deployment.yaml",
    "customer-service-deployment.yaml",
    "job-service-deployment.yaml",
    "customer-contract-service-deployment.yaml",
    "customer-payment-service-deployment.yaml",
    "customer-statistics-service-deployment.yaml",
    "frontend-deployment.yaml",
    "ingress.yaml",
    "hpa.yaml",
    "network-policy.yaml"
)

$validFiles = 0
$totalFiles = $yamlFiles.Count

foreach ($yamlFile in $yamlFiles) {
    $filePath = Join-Path $ScriptDir $yamlFile
    if (Test-Path $filePath) {
        try {
            kubectl apply --dry-run=client -f $filePath 2>$null | Out-Null
            $validFiles++
        } catch {
            Write-Warning "YAML validation failed for $yamlFile"
        }
    } else {
        Write-Error "File not found: $yamlFile"
    }
}

Write-Status "YAML validation: $validFiles/$totalFiles files are valid"

# Check if JAR files exist
Write-Status "Checking if Spring Boot applications are built..."

$services = @("api-gateway", "customer-service", "job-service", "customer-contract-service", "customer-payment-service", "customer-statistics-service")
$builtServices = 0

foreach ($service in $services) {
    $jarFile = Join-Path $ProjectRoot "$service\target\$service-0.0.1-SNAPSHOT.jar"
    if (Test-Path $jarFile) {
        $builtServices++
        Write-Success "$service is built"
    } else {
        Write-Warning "$service is not built (JAR file not found)"
    }
}

Write-Status "Built services: $builtServices/$($services.Count)"

if ($builtServices -lt $services.Count) {
    Write-Warning "Some services are not built. Run 'mvn clean package' in each service directory"
}

# Check if frontend is built
$frontendBuildPath = Join-Path $ProjectRoot "microservice_fe\build"
if (Test-Path $frontendBuildPath) {
    Write-Success "Frontend is built"
} else {
    Write-Warning "Frontend is not built. Run 'npm run build' in microservice_fe directory"
}

# Display next steps
Write-Host "========================================" -ForegroundColor Magenta
Write-Host "SETUP COMPLETED" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta

Write-Host ""
Write-Status "Next steps:"
Write-Host ""
Write-Host "1. Build all applications (if not already built):"
Write-Host "   cd $ProjectRoot"
Write-Host "   .\k8s\build-images.sh"
Write-Host ""
Write-Host "2. Deploy to Kubernetes:"
Write-Host "   .\k8s\deploy.sh"
Write-Host ""
Write-Host "3. Check deployment status:"
Write-Host "   .\k8s\status.sh"
Write-Host ""
Write-Host "4. View logs:"
Write-Host "   .\k8s\logs.sh -a"
Write-Host ""
Write-Host "5. Clean up (when needed):"
Write-Host "   .\k8s\undeploy.sh"
Write-Host ""

Write-Success "🎉 Setup completed! You can now proceed with the deployment."

# Note about shell scripts on Windows
Write-Host ""
Write-Warning "Note: On Windows, you may need to run shell scripts using Git Bash or WSL:"
Write-Host "  - Git Bash: bash ./k8s/deploy.sh"
Write-Host "  - WSL: wsl bash ./k8s/deploy.sh"
Write-Host "  - Or use PowerShell equivalents where available"
