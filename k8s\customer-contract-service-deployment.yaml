apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-contract-service
  namespace: microservice-system
  labels:
    app: customer-contract-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: customer-contract-service
  template:
    metadata:
      labels:
        app: customer-contract-service
    spec:
      containers:
      - name: customer-contract-service
        image: customer-contract-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8083
          name: http
        envFrom:
        - configMapRef:
            name: customer-contract-service-config
        env:
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8083
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8083
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      initContainers:
      - name: wait-for-postgres
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z postgres-service 5432; do echo waiting for postgres; sleep 2; done;']
      - name: wait-for-customer-service
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z customer-service 8081; do echo waiting for customer-service; sleep 2; done;']
      - name: wait-for-job-service
        image: busybox:1.35
        command: ['sh', '-c', 'until nc -z job-service 8082; do echo waiting for job-service; sleep 2; done;']
---
apiVersion: v1
kind: Service
metadata:
  name: customer-contract-service
  namespace: microservice-system
  labels:
    app: customer-contract-service
spec:
  type: ClusterIP
  ports:
  - port: 8083
    targetPort: 8083
    protocol: TCP
    name: http
  selector:
    app: customer-contract-service
